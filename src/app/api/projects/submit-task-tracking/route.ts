import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(req: Request) {
  try {
    const {
      projectId,
      taskId,
      title,
      submittedBy,
      submissionLink,
      notes
    } = await req.json();

    if (!projectId || !title || !submittedBy || !submissionLink) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const tasksFilePath = path.join(process.cwd(), 'data', 'project-tasks', `${projectId}.json`);

    if (!fs.existsSync(tasksFilePath)) {
      return NextResponse.json({ error: 'Project task file not found' }, { status: 404 });
    }

    const fileData = fs.readFileSync(tasksFilePath, 'utf-8');
    const taskList = JSON.parse(fileData);

    let taskUpdated = false;

    const updatedTasks = taskList.map((task: any) => {
      if ((task.id && task.id === taskId) || task.title === title) {
        task.status = 'in review';
        task.submittedBy = submittedBy;
        task.submissionLink = submissionLink;
        task.notes = notes;
        task.lastSubmittedAt = new Date().toISOString();
        taskUpdated = true;
      }
      return task;
    });

    if (!taskUpdated) {
      return NextResponse.json({ error: 'Task not found in project' }, { status: 404 });
    }

    fs.writeFileSync(tasksFilePath, JSON.stringify(updatedTasks, null, 2));

    // Here you can also trigger notifications if needed

    return NextResponse.json({ message: 'Task submitted for review' });
  } catch (err: any) {
    console.error(err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
