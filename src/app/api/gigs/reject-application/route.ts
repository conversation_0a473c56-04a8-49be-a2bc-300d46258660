import { NextResponse } from 'next/server';
import { readFile, writeFile } from 'fs/promises';
import path from 'path';

const APPLICATIONS_PATH = path.join(process.cwd(), 'data/gigs/gig-applications.json');

export async function POST(req: Request) {
  try {
    const { applicationId } = await req.json();

    if (!applicationId) {
      return NextResponse.json(
        { error: 'Missing applicationId' },
        { status: 400 }
      );
    }

    // Read applications data
    const applicationsData = await readFile(APPLICATIONS_PATH, 'utf-8');
    const applications = JSON.parse(applicationsData);

    // Find and update the application
    const applicationIndex = applications.findIndex((app: any) => app.id === applicationId);
    
    if (applicationIndex === -1) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Update application status to rejected
    applications[applicationIndex].status = 'rejected';

    // Save updated applications
    await writeFile(APPLICATIONS_PATH, JSON.stringify(applications, null, 2));

    return NextResponse.json({
      success: true,
      message: 'Application rejected successfully'
    });

  } catch (error) {
    console.error('Error rejecting application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
