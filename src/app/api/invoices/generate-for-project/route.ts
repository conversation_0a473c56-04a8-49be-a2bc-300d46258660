import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, freelancerId } = body;

    if (!projectId || !freelancerId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Fetch project data, tasks, and existing invoices using hierarchical storage
    const { readAllTasks, convertHierarchicalToLegacy } = await import('@/lib/project-tasks/hierarchical-storage');

    const [projectsData, hierarchicalTasks, invoicesData] = await Promise.all([
      fs.promises.readFile(path.join(process.cwd(), 'data', 'projects.json'), 'utf-8'),
      readAllTasks(),
      fs.promises.readFile(path.join(process.cwd(), 'data', 'invoices.json'), 'utf-8')
    ]);

    // Convert tasks to legacy format for compatibility
    const projectTasksData = JSON.stringify(convertHierarchicalToLegacy(hierarchicalTasks));

    const allProjects = JSON.parse(projectsData);
    const allProjectTasks = JSON.parse(projectTasksData);
    const allInvoices = JSON.parse(invoicesData);

    // Find the specific project
    const projectInfo = allProjects.find((p: any) => p.projectId === projectId);
    const projectTasks = allProjectTasks.find((pt: any) => pt.projectId === projectId);

    if (!projectInfo || !projectTasks) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const tasks = projectTasks.tasks || [];

    // Get already invoiced tasks
    const invoicedTaskIds = new Set();
    const invoicedTaskTitles = new Set();
    allInvoices
      .filter((invoice: any) => invoice.projectId === projectId)
      .forEach((invoice: any) => {
        if (invoice.milestones) {
          invoice.milestones.forEach((milestone: any) => {
            if (milestone.taskId) invoicedTaskIds.add(milestone.taskId);
            if (milestone.title || milestone.description) {
              invoicedTaskTitles.add(milestone.title || milestone.description);
            }
          });
        }
        if (invoice.milestoneDescription) {
          invoicedTaskTitles.add(invoice.milestoneDescription);
        }
      });

    // Find available tasks
    const availableTasks = tasks.filter((task: any) => {
      return task.status === 'Approved' &&
             !invoicedTaskIds.has(task.id) &&
             !invoicedTaskTitles.has(task.title) &&
             !task.invoicePaid;
    });

    if (availableTasks.length === 0) {
      return NextResponse.json({ error: 'No available tasks to invoice' }, { status: 400 });
    }

    // Calculate rate per task
    let ratePerTask = 0;
    if (projectInfo.totalBudget && projectInfo.totalTasks) {
      if (projectInfo.invoicingMethod === 'completion') {
        const upfrontCommitment = projectInfo.upfrontCommitment || 0;
        const milestonePool = projectInfo.totalBudget - upfrontCommitment;
        ratePerTask = milestonePool / projectInfo.totalTasks;
      } else {
        ratePerTask = projectInfo.totalBudget / projectInfo.totalTasks;
      }
    }

    // Create milestones from available tasks
    const milestones = availableTasks.map((task: any) => ({
      title: task.title || `Task ${task.id}`,
      description: task.description || task.title || `Task ${task.id}`,
      rate: ratePerTask > 0 ? ratePerTask : 0,
      taskId: task.id
    }));

    const totalAmount = milestones.reduce((sum: number, m: any) => sum + m.rate, 0);

    // Generate invoice number
    const invoiceNumber = `INV-${projectId}-${Date.now()}`;

    // Create invoice data
    const invoiceData = {
      invoiceNumber,
      freelancerId: Number(freelancerId),
      commissionerId: projectInfo.commissionerId,
      projectId: projectId,
      projectTitle: projectInfo.title,
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 14 days from now
      totalAmount: totalAmount,
      status: 'draft',
      milestones: milestones,
      isCustomProject: false,
      createdAt: new Date().toISOString(),
      autoGenerated: true
    };

    // Save to invoices.json
    const invoicesPath = path.join(process.cwd(), 'data', 'invoices.json');
    const invoices = JSON.parse(fs.readFileSync(invoicesPath, 'utf-8'));
    
    const newInvoice = {
      ...invoiceData,
      id: invoices.length > 0 ? Math.max(...invoices.map((inv: any) => inv.id || 0)) + 1 : 1
    };

    invoices.push(newInvoice);
    fs.writeFileSync(invoicesPath, JSON.stringify(invoices, null, 2));

    return NextResponse.json({
      success: true,
      invoiceNumber,
      invoiceData: newInvoice
    });

  } catch (error) {
    console.error('Error generating invoice for project:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
