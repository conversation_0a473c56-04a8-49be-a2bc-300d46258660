{"projectId": 320, "title": "Park Visitor Mobile App", "description": "Develop a mobile app for park visitors to find amenities, book facilities, and get real-time updates.", "organizationId": 1, "typeTags": ["React Native", "Mobile", "iOS", "Android"], "manager": {"name": "<PERSON><PERSON>", "title": "Product Manager", "avatar": "/avatars/neilsan.png", "email": "<EMAIL>"}, "freelancerId": 11, "status": "ongoing", "dueDate": "2025-09-28", "totalTasks": 1, "createdAt": "2025-08-03T14:53:43.061Z"}