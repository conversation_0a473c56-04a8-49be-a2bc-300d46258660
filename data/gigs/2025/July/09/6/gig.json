{"id": 6, "title": "Park Visitor Mobile App", "organizationId": 1, "commissionerId": 32, "category": "Mobile Development", "tags": ["React Native", "Mobile", "iOS", "Android"], "hourlyRateMin": 80, "hourlyRateMax": 120, "description": "Develop a mobile app for park visitors to find amenities, book facilities, and get real-time updates.", "deliveryTimeWeeks": 8, "estimatedHours": 60, "status": "Unavailable", "toolsRequired": ["React Native", "Firebase", "Maps API"], "notes": "Cross-platform compatibility required. Integration with existing park systems.", "postedDate": "2025-07-10"}