[{"id": 201, "freelancerId": 1, "gigId": 101, "organizationId": 1, "commissionerId": 32, "title": "Park Services Website Rebrand", "skills": ["Web Design", "UI/UX Design"], "tools": ["Figma", "Adobe Photoshop"], "notes": "Help us redesign our digital presence for better accessibility.", "budget": {"min": 3500, "max": 5000, "currency": "USD"}, "status": "Accepted", "createdAt": "2025-07-05T10:12:00Z", "responses": [{"type": "accepted", "timestamp": "2025-07-27T18:21:33.003Z", "message": "Offer accepted"}], "acceptedAt": "2025-07-27T18:21:33.003Z"}, {"id": 202, "freelancerId": 3, "gigId": 102, "organizationId": 2, "commissionerId": 33, "title": "Channel Identity Toolkit", "skills": ["Branding", "Brand Design", "Motion Graphics"], "tools": ["Adobe Illustrator", "After Effects"], "notes": "Create identity assets for Urbana's new documentary stream.", "budget": {"min": 2800, "max": 4200, "currency": "USD"}, "status": "accepted", "createdAt": "2025-07-06T14:45:00Z", "responses": [{"type": "accepted", "timestamp": "2025-08-03T20:53:44.277Z", "message": "Offer accepted"}], "acceptedAt": "2025-08-03T20:53:44.277Z", "projectId": 324}, {"id": 203, "freelancerId": 6, "gigId": 103, "organizationId": 3, "commissionerId": 34, "title": "App Promo Video", "skills": ["Video Editing", "Motion Graphics"], "tools": ["Premiere Pro", "After Effects"], "notes": "Edit a 60s vertical promo for Corlax's new wellness app.", "budget": {"min": 1500, "max": 2500, "currency": "USD"}, "status": "Rejected", "createdAt": "2025-07-03T08:20:00Z", "responses": []}, {"id": 204, "freelancerId": 2, "gigId": 104, "organizationId": 5, "commissionerId": 36, "title": "Concept Testing Deck", "skills": ["Design Research", "User Research", "Content Design"], "tools": ["PowerPoint", "Figma", "Mir<PERSON>"], "notes": "Build out visual layouts for an insights deck to present to board.", "budget": {"min": 2200, "max": 3800, "currency": "USD"}, "status": "Accepted", "createdAt": "2025-07-01T17:50:00Z", "responses": []}, {"id": 205, "freelancerId": 5, "gigId": 105, "organizationId": 6, "commissionerId": 37, "title": "Tech Showcase Speaker <PERSON><PERSON>", "skills": ["Video Editing", "Motion Graphics"], "tools": ["After Effects", "Premiere Pro"], "notes": "Edit highlight reel of our keynote speakers and testimonials.", "budget": {"min": 3200, "max": 4800, "currency": "USD"}, "status": "Accepted", "createdAt": "2025-07-07T12:00:00Z", "responses": [{"type": "accepted", "timestamp": "2025-07-28T19:30:39.175Z", "message": "Offer accepted"}], "acceptedAt": "2025-07-28T19:30:39.175Z"}, {"id": 206, "freelancerId": 7, "gigId": 106, "organizationId": 7, "commissionerId": 38, "title": "UX Audit Walkthrough", "skills": ["UX Research", "Heuristic Review", "User Testing"], "tools": ["Notion", "Figma", "<PERSON><PERSON>"], "notes": "Record a UX walkthrough audit of our app based on given heuristics.", "budget": {"min": 2500, "max": 4000, "currency": "USD"}, "status": "Available", "createdAt": "2025-07-07T14:30:00Z", "responses": []}, {"id": 207, "freelancerId": 31, "gigId": 107, "organizationId": 1, "commissionerId": 32, "title": "Content Strategy for Parks Website", "skills": ["Content Strategy", "UX Writing"], "tools": ["Figma", "Google Docs"], "notes": "We need help developing a comprehensive content strategy for our new parks website.", "budget": {"min": 2000, "max": 3500, "currency": "USD"}, "status": "Accepted", "createdAt": "2025-07-08T09:15:00Z", "responses": [{"type": "accepted", "timestamp": "2025-08-03T20:36:02.348Z", "message": "Offer accepted"}], "acceptedAt": "2025-08-03T20:36:02.348Z"}, {"id": 208, "freelancerId": 31, "gigId": 108, "organizationId": 2, "commissionerId": 33, "title": "Brand Voice Guidelines", "skills": ["Brand Strategy", "Content Design"], "tools": ["Adobe InDesign", "Figma"], "notes": "Create comprehensive brand voice guidelines for our documentary channel.", "budget": {"min": 1500, "max": 2500, "currency": "USD"}, "status": "Available", "createdAt": "2025-07-10T16:45:00Z", "responses": []}]